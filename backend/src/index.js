// Environment variables are loaded from the Docker environment
// require('dotenv').config();
const express = require('express');
const cors = require('cors');
const morgan = require('morgan');
const { PrismaClient } = require('@prisma/client');

// Import routes
const path = require('path');
const authRoutes = require('./routes/auth');
const userRoutes = require('./routes/users');
const onboardingRoutes = require('./routes/onboarding');
const mealRoutes = require('./routes/meals');
const goalRoutes = require('./routes/goals');
const analyzeRoutes = require('./routes/analyze');
const exerciseRoutes = require('./routes/exercises');
const waterRoutes = require('./routes/water');
const weightRoutes = require('./routes/weight');
const foodRoutes = require('./routes/foods');
const uploadsRoutes = require('./routes/uploads');

// Initialize Express app
const app = express();
const prisma = new PrismaClient();

// Middleware
app.use(cors());
app.use(express.json());
app.use(morgan('dev'));
// Serve uploads directory at both /uploads and root level for better compatibility
app.use('/uploads', express.static(path.join(__dirname, '../uploads')));
app.use('/uploads/meals', express.static(path.join(__dirname, '../uploads/meals')));

// Log when static file middleware is set up
console.log('Static file middleware configured:');
console.log('- Serving files from:', path.join(__dirname, '../uploads'));
console.log('- Serving meal images from:', path.join(__dirname, '../uploads/meals'));

// Custom request logger middleware for detailed debugging
app.use((req, res, next) => {
  // Only log for specific routes that need debugging
  if (req.originalUrl.includes('/api/meals') || req.originalUrl.includes('/api/analyze')) {
    console.log(`\n===== REQUEST ${req.method} ${req.originalUrl} =====`);
    console.log('Headers:', JSON.stringify(req.headers, null, 2));

    // Log request body for POST/PUT requests
    if (req.method === 'POST' || req.method === 'PUT') {
      console.log('Body:', JSON.stringify(req.body, null, 2));
    }

    // Log query parameters
    if (Object.keys(req.query).length > 0) {
      console.log('Query params:', JSON.stringify(req.query, null, 2));
    }

    // Capture the original send method
    const originalSend = res.send;

    // Override the send method to log the response
    res.send = function(body) {
      console.log(`\n===== RESPONSE ${req.method} ${req.originalUrl} =====`);
      console.log('Status:', res.statusCode);

      // Try to parse and log the response body if it's JSON
      try {
        const parsedBody = typeof body === 'string' ? JSON.parse(body) : body;
        console.log('Response body:', JSON.stringify(parsedBody, null, 2));
      } catch (e) {
        console.log('Response body: [Unable to parse response body]');
      }

      // Call the original send method
      return originalSend.call(this, body);
    };
  }

  next();
});

// Make Prisma available to routes
app.use((req, res, next) => {
  req.prisma = prisma;
  next();
});

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/onboarding', onboardingRoutes);
app.use('/api/meals', mealRoutes);
app.use('/api/goals', goalRoutes);
app.use('/api/analyze', analyzeRoutes);
app.use('/api/exercises', exerciseRoutes);
app.use('/api/water', waterRoutes);
app.use('/api/weight', weightRoutes);
app.use('/api/foods', foodRoutes);
app.use('/api/uploads', uploadsRoutes);

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok' });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    error: 'Internal Server Error',
    message: process.env.NODE_ENV === 'development' ? err.message : undefined
  });
});

// Start server
const PORT = process.env.PORT || 86;
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});

// Handle graceful shutdown
process.on('SIGTERM', async () => {
  console.log('SIGTERM received, shutting down gracefully');
  await prisma.$disconnect();
  process.exit(0);
});

process.on('SIGINT', async () => {
  console.log('SIGINT received, shutting down gracefully');
  await prisma.$disconnect();
  process.exit(0);
});
