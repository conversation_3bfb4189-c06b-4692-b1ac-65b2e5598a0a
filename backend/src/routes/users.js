const express = require('express');
const { body, validationResult } = require('express-validator');
const router = express.Router();
const jwt = require('jsonwebtoken');
const { encryptApiKey, decryptApiKey, validateEncryptedApiKey } = require('../services/encryptionService');

// Middleware to verify JWT token
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ error: 'Forbidden' });
    }
    req.user = user;
    next();
  });
};

// Get user profile
router.get('/profile', authenticateToken, async (req, res) => {
  try {
    const user = await req.prisma.user.findUnique({
      where: { id: req.user.userId },
      select: {
        id: true,
        email: true,
        gender: true,
        dob: true,
        heightCm: true,
        weightKg: true,
        startingWeightKg: true,
        goalType: true,
        targetWeight: true,
        activityLevel: true,
        heightUnit: true,
        weightUnit: true,
        apiProvider: true,
        apiModel: true,
        hasApiKeyConfigured: true,
        onboardingCompleted: true,
        onboardingStep: true,
        onboardingStartedAt: true,
        onboardingCompletedAt: true,
        createdAt: true,
        updatedAt: true
      }
    });

    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    res.json(user);
  } catch (error) {
    console.error('Error fetching user profile:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Update user profile
router.put('/profile',
  authenticateToken,
  [
    body('gender').optional().isString(),
    body('dob').optional().isISO8601(),
    body('heightCm').optional().isFloat({ min: 50, max: 300 }),
    body('weightKg').optional().isFloat({ min: 20, max: 500 }),
    body('goalType').optional().isIn(['lose', 'maintain', 'gain']),
    body('targetWeight').optional().isFloat({ min: 20, max: 500 }),
    body('activityLevel').optional().isIn(['sedentary', 'light', 'moderate', 'active', 'very_active']),
    body('heightUnit').optional().isIn(['cm', 'ft']),
    body('weightUnit').optional().isIn(['kg', 'lbs']),
    body('apiProvider').optional().isIn(['openai', 'gemini']),
    body('apiModel').optional().isString(),
    body('hasApiKeyConfigured').optional().isBoolean()
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const updatedUser = await req.prisma.user.update({
        where: { id: req.user.userId },
        data: {
          gender: req.body.gender,
          dob: req.body.dob ? new Date(req.body.dob) : undefined,
          heightCm: req.body.heightCm,
          weightKg: req.body.weightKg,
          // Note: startingWeightKg is intentionally excluded - it cannot be modified after signup
          goalType: req.body.goalType,
          targetWeight: req.body.targetWeight,
          activityLevel: req.body.activityLevel,
          heightUnit: req.body.heightUnit,
          weightUnit: req.body.weightUnit,
          apiProvider: req.body.apiProvider,
          apiModel: req.body.apiModel,
          hasApiKeyConfigured: req.body.hasApiKeyConfigured
        },
        select: {
          id: true,
          email: true,
          gender: true,
          dob: true,
          heightCm: true,
          weightKg: true,
          startingWeightKg: true,
          goalType: true,
          targetWeight: true,
          activityLevel: true,
          heightUnit: true,
          weightUnit: true,
          apiProvider: true,
          apiModel: true,
          hasApiKeyConfigured: true,
          updatedAt: true
        }
      });

      res.json(updatedUser);
    } catch (error) {
      console.error('Error updating user profile:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

// Get user streak
router.get('/streak', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;
    const today = new Date();

    // Calculate streak by checking consecutive days with activity
    let streak = 0;
    let currentDate = new Date(today);

    // Check each day going backwards from today
    while (true) {
      const startOfDay = new Date(currentDate);
      startOfDay.setHours(0, 0, 0, 0);

      const endOfDay = new Date(currentDate);
      endOfDay.setHours(23, 59, 59, 999);

      // Check if user had any activity on this day
      const [mealsCount, exercisesCount, weightLogsCount] = await Promise.all([
        req.prisma.mealLogged.count({
          where: {
            userId: userId,
            timestamp: {
              gte: startOfDay,
              lte: endOfDay
            }
          }
        }),
        req.prisma.exercise.count({
          where: {
            userId: userId,
            timestamp: {
              gte: startOfDay,
              lte: endOfDay
            }
          }
        }),
        req.prisma.weightLog.count({
          where: {
            userId: userId,
            timestamp: {
              gte: startOfDay,
              lte: endOfDay
            }
          }
        })
      ]);

      const hasActivity = mealsCount > 0 || exercisesCount > 0 || weightLogsCount > 0;

      if (hasActivity) {
        streak++;
        // Move to previous day
        currentDate.setDate(currentDate.getDate() - 1);
      } else {
        // No activity found, break the streak
        break;
      }

      // Safety check to prevent infinite loop (max 365 days)
      if (streak >= 365) {
        break;
      }
    }

    res.json({ streak });
  } catch (error) {
    console.error('Error calculating user streak:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Store API key
router.post('/api-key', authenticateToken, [
  body('provider').isIn(['openai', 'gemini']).withMessage('Provider must be openai or gemini'),
  body('model').isString().notEmpty().withMessage('Model is required'),
  body('apiKey').isString().notEmpty().withMessage('API key is required')
], async (req, res) => {
  try {
    // Validate request
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { provider, model, apiKey } = req.body;

    // Encrypt the API key
    const encryptedApiKey = encryptApiKey(apiKey);

    // Update user with API configuration
    const updatedUser = await req.prisma.user.update({
      where: { id: req.user.userId },
      data: {
        apiProvider: provider,
        apiModel: model,
        hasApiKeyConfigured: true,
        encryptedApiKey: encryptedApiKey
      },
      select: {
        id: true,
        apiProvider: true,
        apiModel: true,
        hasApiKeyConfigured: true
      }
    });

    res.json({
      success: true,
      user: updatedUser
    });
  } catch (error) {
    console.error('Error storing API key:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get API key (decrypted for use)
router.get('/api-key', authenticateToken, async (req, res) => {
  try {
    const user = await req.prisma.user.findUnique({
      where: { id: req.user.userId },
      select: {
        apiProvider: true,
        apiModel: true,
        hasApiKeyConfigured: true,
        encryptedApiKey: true
      }
    });

    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    if (!user.hasApiKeyConfigured || !user.encryptedApiKey) {
      return res.status(404).json({ error: 'No API key configured' });
    }

    try {
      // Decrypt the API key
      const decryptedApiKey = decryptApiKey(user.encryptedApiKey);

      res.json({
        provider: user.apiProvider,
        model: user.apiModel,
        apiKey: decryptedApiKey,
        isConfigured: true
      });
    } catch (decryptError) {
      console.error('Error decrypting API key:', decryptError);
      res.status(500).json({ error: 'Failed to decrypt API key' });
    }
  } catch (error) {
    console.error('Error retrieving API key:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Delete API key
router.delete('/api-key', authenticateToken, async (req, res) => {
  try {
    await req.prisma.user.update({
      where: { id: req.user.userId },
      data: {
        apiProvider: null,
        apiModel: null,
        hasApiKeyConfigured: false,
        encryptedApiKey: null
      }
    });

    res.json({ success: true });
  } catch (error) {
    console.error('Error deleting API key:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Mark onboarding as completed
router.post('/complete-onboarding', authenticateToken, async (req, res) => {
  try {
    const updatedUser = await req.prisma.user.update({
      where: { id: req.user.userId },
      data: {
        onboardingCompleted: true,
        onboardingCompletedAt: new Date()
      },
      select: {
        id: true,
        onboardingCompleted: true,
        onboardingCompletedAt: true
      }
    });

    res.json({
      success: true,
      message: 'Onboarding marked as completed',
      user: updatedUser
    });
  } catch (error) {
    console.error('Error completing onboarding:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
