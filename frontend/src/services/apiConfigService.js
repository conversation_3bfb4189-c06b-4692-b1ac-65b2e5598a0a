import axios from 'axios';

// Available AI providers and their models
export const AI_PROVIDERS = {
  openai: {
    name: 'OpenAI',
    models: [
      { id: 'gpt-4o', name: 'GPT-4o', supportsVision: true },
      { id: 'gpt-4o-mini', name: 'GPT-4o Mini', supportsVision: true },
      { id: 'gpt-4.1', name: 'GPT-4.1', supportsVision: true },
      { id: 'gpt-4.1-mini', name: 'GPT-4.1 Mini', supportsVision: true },
      { id: 'gpt-4.1-nano', name: 'GPT-4.1 Nano', supportsVision: true },
      { id: 'gpt-4.5', name: 'GPT-4.5', supportsVision: true },
      { id: 'o1', name: 'o1', supportsVision: true },
      { id: 'o1-pro', name: 'o1 Pro', supportsVision: true },
      { id: 'o3', name: 'o3', supportsVision: true },
      { id: 'o4-mini', name: 'o4 Mini', supportsVision: true },
    ]
  },
  gemini: {
    name: 'Google Gemini',
    models: [
      { id: 'gemini-1.5-pro', name: 'Gemini 1.5 Pro', supportsVision: true },
      { id: 'gemini-1.5-flash', name: 'Gemini 1.5 Flash', supportsVision: true },
      { id: 'gemini-pro-vision', name: 'Gemini Pro Vision', supportsVision: true },
    ]
  }
};

// Get models that support vision for a provider
export const getVisionModels = (provider) => {
  if (!AI_PROVIDERS[provider]) return [];
  return AI_PROVIDERS[provider].models.filter(model => model.supportsVision);
};

// API endpoints for backend communication
const API_ENDPOINTS = {
  STORE_API_KEY: '/api/users/api-key',
  GET_API_KEY: '/api/users/api-key',
  DELETE_API_KEY: '/api/users/api-key'
};

// Save API configuration to backend
export const saveApiConfig = async (provider, model, apiKey) => {
  try {
    const response = await axios.post(API_ENDPOINTS.STORE_API_KEY, {
      provider,
      model,
      apiKey
    });

    return response.data.success;
  } catch (error) {
    console.error('Error saving API configuration:', error);
    return false;
  }
};

// Load API configuration from backend
export const loadApiConfig = async () => {
  try {
    const response = await axios.get(API_ENDPOINTS.GET_API_KEY);
    return {
      provider: response.data.provider,
      model: response.data.model,
      apiKey: response.data.apiKey,
      isConfigured: response.data.isConfigured
    };
  } catch (error) {
    if (error.response?.status === 404) {
      // No API key configured
      return null;
    }
    console.error('Error loading API configuration:', error);
    return null;
  }
};

// Clear API configuration
export const clearApiConfig = async () => {
  try {
    await axios.delete(API_ENDPOINTS.DELETE_API_KEY);
    return true;
  } catch (error) {
    console.error('Error clearing API configuration:', error);
    return false;
  }
};

// Check if API is configured
export const isApiConfigured = async () => {
  try {
    const config = await loadApiConfig();
    return config !== null && config.isConfigured;
  } catch (error) {
    return false;
  }
};

// Validate OpenAI API key
export const validateOpenAIKey = async (apiKey) => {
  try {
    const response = await fetch('https://api.openai.com/v1/models', {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      return { valid: true, error: null };
    } else if (response.status === 401) {
      return { valid: false, error: 'Invalid API key' };
    } else {
      return { valid: false, error: 'Unable to validate API key' };
    }
  } catch (error) {
    console.error('Error validating OpenAI API key:', error);
    return { valid: false, error: 'Network error during validation' };
  }
};

// Validate Gemini API key
export const validateGeminiKey = async (apiKey) => {
  try {
    // Use a simple request to validate the Gemini API key
    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models?key=${apiKey}`);

    if (response.ok) {
      return { valid: true, error: null };
    } else if (response.status === 400 || response.status === 403) {
      return { valid: false, error: 'Invalid API key' };
    } else {
      return { valid: false, error: 'Unable to validate API key' };
    }
  } catch (error) {
    console.error('Error validating Gemini API key:', error);
    return { valid: false, error: 'Network error during validation' };
  }
};

// Validate API key based on provider
export const validateApiKey = async (provider, apiKey) => {
  if (!apiKey || apiKey.trim() === '') {
    return { valid: false, error: 'API key is required' };
  }

  switch (provider) {
    case 'openai':
      return await validateOpenAIKey(apiKey);
    case 'gemini':
      return await validateGeminiKey(apiKey);
    default:
      return { valid: false, error: 'Unsupported provider' };
  }
};

// Get current API configuration for making requests
export const getCurrentApiConfig = () => {
  const config = loadApiConfig();
  if (!config) {
    throw new Error('API not configured. Please configure your API settings.');
  }
  return config;
};
