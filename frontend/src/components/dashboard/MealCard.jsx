import { classNames } from '../../utils/classNames';
import { BreakfastIcon, LunchIcon, DinnerIcon, SnackIcon, ProteinIcon, CarbsIcon, FatsIcon } from '../../assets/icons/DashboardIcons';

// Helper function to ensure image URLs are absolute
const getAbsoluteImageUrl = (imageUrl) => {
  if (!imageUrl) return null;

  // If it's already an absolute URL, return it as is
  if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
    return imageUrl;
  }

  // If it's a relative URL starting with /uploads, make it absolute
  if (imageUrl.startsWith('/uploads')) {
    // Use the current host with the correct port
    const host = window.location.hostname;
    const port = window.location.port || '86'; // Default to port 86 if not specified
    return `http://${host}:${port}${imageUrl}`;
  }

  // Return the original URL if it doesn't match any of the above conditions
  return imageUrl;
};

/**
 * MealCard component for displaying meal information
 *
 * @param {Object} meal - Meal data object
 * @param {Function} onDelete - Function to call when delete button is clicked
 * @param {string} className - Additional CSS classes
 */
const MealCard = ({ meal, onDelete, className }) => {
  // Determine meal type based on time
  const getMealType = (timestamp) => {
    const hour = new Date(timestamp).getHours();

    if (hour >= 5 && hour < 10) return 'breakfast';
    if (hour >= 10 && hour < 15) return 'lunch';
    if (hour >= 15 && hour < 21) return 'dinner';
    return 'snack';
  };

  // Get icon based on meal type
  const getMealIcon = (type) => {
    switch (type) {
      case 'breakfast':
        return <BreakfastIcon className="w-5 h-5" />;
      case 'lunch':
        return <LunchIcon className="w-5 h-5" />;
      case 'dinner':
        return <DinnerIcon className="w-5 h-5" />;
      default:
        return <SnackIcon className="w-5 h-5" />;
    }
  };

  // Get background color based on meal type
  const getMealColor = (type) => {
    switch (type) {
      case 'breakfast':
        return 'bg-yellow-50 text-yellow-600';
      case 'lunch':
        return 'bg-green-50 text-green-600';
      case 'dinner':
        return 'bg-blue-50 text-blue-600';
      default:
        return 'bg-purple-50 text-purple-600';
    }
  };

  const mealType = getMealType(meal.timestamp);
  const mealIcon = getMealIcon(mealType);
  const mealColor = getMealColor(mealType);

  return (
    <div className={classNames(
      "bg-white rounded-xl border border-gray-100 overflow-hidden transition-all hover:shadow-md w-full max-w-md h-24",
      className
    )}>
      <div className="flex items-start p-4 h-full">
        {/* Meal image or placeholder */}
        <div className="flex-shrink-0 mr-4 w-16">
          {/* Check for either imageUrl (backend) or image_url (frontend) */}
          {(meal.imageUrl || meal.image_url) ? (
            <div className="w-16 h-16 rounded-lg overflow-hidden relative">
              <img
                src={getAbsoluteImageUrl(meal.imageUrl || meal.image_url)}
                alt={meal.name}
                className="w-full h-full object-cover"
                onError={(e) => {
                  console.error('Image failed to load:', meal.imageUrl || meal.image_url);
                  console.error('Absolute URL was:', getAbsoluteImageUrl(meal.imageUrl || meal.image_url));
                  // Log error details for debugging
                  console.log('Image error details:', {
                    component: 'MealCard',
                    mealId: meal.id,
                    imageSrc: meal.imageUrl || meal.image_url,
                    absoluteImageSrc: getAbsoluteImageUrl(meal.imageUrl || meal.image_url),
                    mealName: meal.name,
                    timestamp: new Date().toISOString()
                  });
                }}
              />
              {/* Time stamp overlay - pill style */}
              <div className="absolute bottom-3 right-2 bg-white bg-opacity-80 text-[10px] px-2 py-0.5 rounded-full text-gray-700 font-medium">
                {new Date(meal.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
              </div>
            </div>
          ) : (
            <div className="w-16 h-16 rounded-lg bg-gray-100 flex items-center justify-center">
              <div className={`p-2 rounded-full ${mealColor}`}>
                {mealIcon}
              </div>
            </div>
          )}
        </div>

        {/* Meal details */}
        <div className="flex-1 min-w-0 flex flex-col justify-between">
          <div>
            <div className="flex justify-between mb-1">
              <div className="h-8 pr-2 flex-1">
                <h3 className={`font-medium text-gray-900 line-clamp-2 ${meal.name.length > 20 ? 'text-xs' : meal.name.length > 15 ? 'text-sm' : 'text-base'}`}>{meal.name}</h3>
              </div>
              <span className="text-sm font-bold whitespace-nowrap flex-shrink-0">{meal.calories} kcal</span>
            </div>

            <div className="flex items-center mb-1">
              <span className={`text-xs px-2 py-0.5 rounded-full ${mealColor}`}>
                {mealType.charAt(0).toUpperCase() + mealType.slice(1)}
              </span>
            </div>
          </div>

          {/* Macros in a single line */}
          <div className="flex items-center gap-3">
            <div className="flex items-center">
              <ProteinIcon className="w-4 h-4 text-blue-700 mr-1" />
              <span className="text-xs text-blue-700">{Math.round(meal.protein_g)}g</span>
            </div>
            <div className="flex items-center">
              <CarbsIcon className="w-4 h-4 text-green-700 mr-1" />
              <span className="text-xs text-green-700">{Math.round(meal.carbs_g)}g</span>
            </div>
            <div className="flex items-center">
              <FatsIcon className="w-4 h-4 text-yellow-700 mr-1" />
              <span className="text-xs text-yellow-700">{Math.round(meal.fats_g)}g</span>
            </div>
          </div>
        </div>

        {/* Delete button */}
        {onDelete && (
          <button
            onClick={() => onDelete(meal.id)}
            className="ml-2 text-gray-400 hover:text-red-500"
            aria-label="Delete meal"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </button>
        )}
      </div>
    </div>
  );
};

export default MealCard;
